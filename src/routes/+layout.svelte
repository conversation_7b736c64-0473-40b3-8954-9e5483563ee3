<script lang="ts">
	import '../app.css';
	import { user, supabase } from '$lib/supabase';
	import AuthModal from '$lib/components/AuthModal.svelte';
	import SettingsIcon from '$lib/components/SettingsIcon.svelte';
	import SettingsPopup from '$lib/components/SettingsPopup.svelte';
	import { themeSettings, colorSchemes } from '$lib/stores/theme';

	let { children } = $props();
	let showAuthModal = $state(false);
	let showSettingsPopup = $state(false);

	async function handleSignOut() {
		await supabase.auth.signOut();
	}

	function toggleSettingsPopup() {
		showSettingsPopup = !showSettingsPopup;
	}

	// Get current color scheme classes
	const currentColorScheme = $derived(colorSchemes[$themeSettings.colorScheme]);
</script>

<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
	<!-- Header -->
	<header class="bg-gradient-to-r {currentColorScheme.colors.primary}/90 backdrop-blur-md shadow-2xl border-b border-white/30 sticky top-0 z-50">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="flex justify-between items-center h-16">
				<a href="/" class="flex items-center space-x-3 hover:scale-105 transition-transform duration-200">
					<div class="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center border border-white/30">
						<span class="text-white text-lg">🗺️</span>
					</div>
					<div>
						<h1 class="text-xl font-bold text-white">
							Mapa Brna
						</h1>
						<p class="text-xs text-white/80">Užitečná místa ve městě</p>
					</div>
				</a>
				<nav class="hidden md:flex items-center space-x-2">
					<a href="/" class="text-white/90 hover:text-white hover:bg-white/20 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200">
						🗺️ Mapa
					</a>
					<a href="/places" class="text-white/90 hover:text-white hover:bg-white/20 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200">
						📍 Místa
					</a>

					{#if $user}
						<a href="/add" class="bg-white/20 backdrop-blur-sm text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-white/30 transition-all duration-200 shadow-lg hover:shadow-xl border border-white/30">
							✨ Přidat místo
						</a>
						<div class="flex items-center space-x-2 ml-4">
							<span class="text-sm text-white/90">👋 {$user?.email || 'Uživatel'}</span>
							<button
								onclick={handleSignOut}
								class="text-sm text-white/80 hover:text-white px-2 py-1 rounded transition-colors hover:bg-white/20"
							>
								Odhlásit
							</button>
						</div>
					{:else}
						<button
							onclick={() => showAuthModal = true}
							class="bg-white/20 backdrop-blur-sm text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-white/30 transition-all duration-200 shadow-lg hover:shadow-xl border border-white/30"
						>
							🔐 Přihlásit
						</button>
					{/if}

					<!-- Settings Icon -->
					<div class="ml-2">
						<SettingsIcon onclick={toggleSettingsPopup} />
					</div>
				</nav>
				<!-- Mobile menu button -->
				<div class="md:hidden">
					<button class="text-white/90 hover:text-white p-2 rounded-lg hover:bg-white/20 transition-colors" aria-label="Otevřít menu">
						<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
						</svg>
					</button>
				</div>
			</div>
		</div>
	</header>

	<!-- Main content -->
	<main class="flex-1 overflow-x-hidden">
		{@render children()}
	</main>
</div>

<!-- Auth Modal -->
<AuthModal
	bind:isOpen={showAuthModal}
	on:close={() => showAuthModal = false}
	on:success={() => showAuthModal = false}
/>

<!-- Settings Popup -->
<SettingsPopup
	bind:isOpen={showSettingsPopup}
	onclose={() => showSettingsPopup = false}
/>

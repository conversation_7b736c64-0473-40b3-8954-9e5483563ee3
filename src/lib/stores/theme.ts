import { writable } from 'svelte/store';
import { browser } from '$app/environment';

// Map style types
export type MapStyle = 'auto' | 'day' | 'night' | 'dawn' | 'dusk';

// UI color scheme types
export type ColorScheme = 'blue' | 'purple' | 'green' | 'orange' | 'pink' | 'teal';

// Theme settings interface
export interface ThemeSettings {
	mapStyle: MapStyle;
	colorScheme: ColorScheme;
}

// Default theme settings
const defaultSettings: ThemeSettings = {
	mapStyle: 'auto',
	colorScheme: 'blue'
};

// Load settings from localStorage
function loadSettings(): ThemeSettings {
	if (!browser) return defaultSettings;

	try {
		const stored = localStorage.getItem('mapa-brna-theme');
		if (stored) {
			return { ...defaultSettings, ...JSON.parse(stored) };
		}
	} catch (error) {
		console.warn('Failed to load theme settings:', error);
	}

	return defaultSettings;
}

// Create the theme store
export const themeSettings = writable<ThemeSettings>(loadSettings());

// Save settings to localStorage when they change
if (browser) {
	themeSettings.subscribe((settings) => {
		try {
			localStorage.setItem('mapa-brna-theme', JSON.stringify(settings));
			// Apply color scheme to document
			applyColorScheme(settings.colorScheme);
		} catch (error) {
			console.warn('Failed to save theme settings:', error);
		}
	});
}

// Color scheme definitions
export const colorSchemes: Record<ColorScheme, { name: string; icon: string; colors: Record<string, string> }> = {
	blue: {
		name: 'Modrá',
		icon: '🔵',
		colors: {
			primary: 'from-blue-600 via-purple-600 to-blue-800',
			accent: 'from-blue-500 to-purple-600',
			button: 'from-blue-600 to-purple-600'
		}
	},
	purple: {
		name: 'Fialová',
		icon: '🟣',
		colors: {
			primary: 'from-purple-600 via-pink-600 to-purple-800',
			accent: 'from-purple-500 to-pink-600',
			button: 'from-purple-600 to-pink-600'
		}
	},
	green: {
		name: 'Zelená',
		icon: '🟢',
		colors: {
			primary: 'from-green-600 via-emerald-600 to-green-800',
			accent: 'from-green-500 to-emerald-600',
			button: 'from-green-600 to-emerald-600'
		}
	},
	orange: {
		name: 'Oranžová',
		icon: '🟠',
		colors: {
			primary: 'from-orange-600 via-red-600 to-orange-800',
			accent: 'from-orange-500 to-red-600',
			button: 'from-orange-600 to-red-600'
		}
	},
	pink: {
		name: 'Růžová',
		icon: '🩷',
		colors: {
			primary: 'from-pink-600 via-rose-600 to-pink-800',
			accent: 'from-pink-500 to-rose-600',
			button: 'from-pink-600 to-rose-600'
		}
	},
	teal: {
		name: 'Tyrkysová',
		icon: '🔷',
		colors: {
			primary: 'from-teal-600 via-cyan-600 to-teal-800',
			accent: 'from-teal-500 to-cyan-600',
			button: 'from-teal-600 to-cyan-600'
		}
	}
};

// Apply color scheme to document
function applyColorScheme(scheme: ColorScheme) {
	if (!browser) return;

	const root = document.documentElement;
	const colors = colorSchemes[scheme].colors;

	// Set CSS custom properties for dynamic theming
	root.style.setProperty('--theme-primary', colors.primary);
	root.style.setProperty('--theme-accent', colors.accent);
	root.style.setProperty('--theme-button', colors.button);
}

// Helper functions
export function updateMapStyle(style: MapStyle) {
	themeSettings.update(settings => ({ ...settings, mapStyle: style }));
}

export function updateColorScheme(scheme: ColorScheme) {
	themeSettings.update(settings => ({ ...settings, colorScheme: scheme }));
}

// Get current auto map style based on time
export function getAutoMapStyle(): string {
	const hour = new Date().getHours();

	if (hour >= 6 && hour < 10) {
		return 'mapbox://styles/mapbox/light-v11'; // Dawn - světlý
	} else if (hour >= 10 && hour < 18) {
		return 'mapbox://styles/mapbox/standard'; // Day - standardní
	} else if (hour >= 18 && hour < 21) {
		return 'mapbox://styles/mapbox/standard-satellite'; // Dusk - satelit
	} else {
		return 'mapbox://styles/mapbox/dark-v11'; // Night - tmavý
	}
}

// Map style definitions
export const mapStyles = [
	{
		id: 'auto' as MapStyle,
		name: 'Automaticky',
		icon: '🌅',
		description: 'Podle času dne',
		style: 'auto'
	},
	{
		id: 'day' as MapStyle,
		name: 'Den',
		icon: '☀️',
		description: 'Standardní mapa',
		style: 'mapbox://styles/mapbox/standard'
	},
	{
		id: 'night' as MapStyle,
		name: 'Noc',
		icon: '🌙',
		description: 'Tmavý režim',
		style: 'mapbox://styles/mapbox/dark-v11'
	},
	{
		id: 'dawn' as MapStyle,
		name: 'Světlý',
		icon: '🌄',
		description: 'Světlé barvy',
		style: 'mapbox://styles/mapbox/light-v11'
	},
	{
		id: 'dusk' as MapStyle,
		name: 'Satelit',
		icon: '🛰️',
		description: 'Satelitní snímky',
		style: 'mapbox://styles/mapbox/standard-satellite'
	}
];
